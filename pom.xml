<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- Basic Maven coordinates -->
    <groupId>io.syrix</groupId>
    <artifactId>syrix-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>Syrix Parent Project</name>
    <description>Parent project for Syrix applications</description>

    <!-- Inherit from Spring Boot Parent -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.2</version>
        <relativePath/>
    </parent>

    <!-- Define modules -->
    <modules>
        <module>SyrixCommon/SyrixDM</module>
        <module>SyrixCommon/SyrixCommonServices</module>
        <module>SyrixCommon/SyrixMessaging</module>
        <module>SyrixCommon/SyrixBaselinesUtils</module>
        <module>SyrixBackend</module>
        <module>SyrixWEB/SyrixPortal</module>
    </modules>

    <!-- Common properties -->
    <properties>
        <!-- Java and encoding -->
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- Dependency versions -->
        <jackson.version>2.18.2</jackson.version>
        <slf4j.version>2.0.17</slf4j.version>
        <logback.version>1.5.15</logback.version>
        <!-- <junit.version>5.10.0</junit.version> REMOVED - Let Spring Boot 3.4.2 manage JUnit versions -->
        <mockito.version>5.6.0</mockito.version>
        <assertj.version>3.24.2</assertj.version>
        <!-- Apache Commons -->
        <apache-commons-collections4.version>4.5.0-M3</apache-commons-collections4.version>
        <apache-commons-text.version>1.14.0</apache-commons-text.version>
        <apache-commons-lang3.version>3.18.0</apache-commons-lang3.version>
        <apache-compiler-plugin.version>3.14.0</apache-compiler-plugin.version>
        <apache-maven-surefire-plugin.version>3.5.3</apache-maven-surefire-plugin.version>
        <apache-maven-javadoc-plugin.version>3.6.0</apache-maven-javadoc-plugin.version>
        <apache-exec-maven-plugin.version>3.1.1</apache-exec-maven-plugin.version>

        <!-- AWS SDK -->
        <aws-sdk.version>2.31.77</aws-sdk.version>


        <azure.identity.version>1.17.0</azure.identity.version>
        <azure.resourcemanager.dns.version>2.35.0</azure.resourcemanager.dns.version>
        <msal4j.version>1.23.0</msal4j.version>
        <gson.version>2.11.0</gson.version>
        <json.version>20240303</json.version>
        <spotbugs-annotations.version>4.7.3</spotbugs-annotations.version>
        <handlebars.version>4.4.0</handlebars.version>
        <guava.version>33.4.0-jre</guava.version>
        <nimbus-jose-jwt.version>10.4.1</nimbus-jose-jwt.version>
        <oauth2-oidc-sdk.version>11.27</oauth2-oidc-sdk.version>
        <spring.context.version>6.2.10</spring.context.version>
        <langchain4j.version>1.0.0-alpha1</langchain4j.version>
        <!-- Syrix -->
        <syrix-data-model.version>1.0.0-SNAPSHOT</syrix-data-model.version>
        <syrix-common-services.version>1.0.0-SNAPSHOT</syrix-common-services.version>
        <syrix-messaging.version>1.0.0-SNAPSHOT</syrix-messaging.version>
        <syrix-utils.version>1.0.0-SNAPSHOT</syrix-utils.version>
        <sonar.organization>syrixio</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
    </properties>

    <!-- Dependency management -->
    <dependencyManagement>
        <dependencies>
            <!-- Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- Logging -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- Azure and Microsoft -->
            <dependency>
                <groupId>com.microsoft.azure</groupId>
                <artifactId>msal4j</artifactId>
                <version>${msal4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-identity</artifactId>
                <version>${azure.identity.version}</version>
            </dependency>
            <dependency>
                <groupId>com.azure.resourcemanager</groupId>
                <artifactId>azure-resourcemanager-dns</artifactId>
                <version>${azure.resourcemanager.dns.version}</version>
            </dependency>
            <dependency>
                <groupId>com.azure.resourcemanager</groupId>
                <artifactId>azure-resourcemanager</artifactId>
                <version>${azure.resourcemanager.dns.version}</version>
            </dependency>

            <!-- JSON -->
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!-- Testing - JUnit 5 dependency management -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>5.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>5.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>5.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-commons</artifactId>
                <version>1.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-launcher</artifactId>
                <version>1.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Apache Commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${apache-commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${apache-commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${apache-commons-lang3.version}</version>
            </dependency>
            <!-- Other Dependencies -->
            <dependency>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-annotations</artifactId>
                <version>${spotbugs-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jknack</groupId>
                <artifactId>handlebars</artifactId>
                <version>${handlebars.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- Spring -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.context.version}</version>
            </dependency>

            <!-- LangChain4j -->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-core</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-open-ai</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-anthropic</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-google-ai-gemini</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>

            <!-- Authentication & Security -->
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus-jose-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>oauth2-oidc-sdk</artifactId>
                <version>${oauth2-oidc-sdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- Build configuration -->
    <build>
        <pluginManagement>
            <plugins>
                <!-- Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${apache-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>

                <!-- Test Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${apache-maven-surefire-plugin.version}</version>
                    <configuration>
                        <argLine>-XX:+EnableDynamicAgentLoading</argLine>
                    </configuration>
                </plugin>

                <!-- Assembly Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.5.0</version>
                    <configuration>
                        <descriptorRefs>
                            <descriptorRef>jar-with-dependencies</descriptorRef>
                        </descriptorRefs>
                    </configuration>
                </plugin>

                <!-- Source Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.0</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>


                <!-- Spring Boot Maven Plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <excludeDevtools>false</excludeDevtools>
                        <jvmArguments>
                            -Dspring.output.ansi.enabled=always
                        </jvmArguments>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Maven Dependency Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.6.1</version>
                    <executions>
                        <execution>
                            <id>copy-dependencies</id>
                            <phase>install</phase>
                            <goals>
                                <goal>copy-dependencies</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${project.build.directory}/lib</outputDirectory>
                                <overWriteReleases>false</overWriteReleases>
                                <overWriteSnapshots>false</overWriteSnapshots>
                                <overWriteIfNewer>true</overWriteIfNewer>
                                <excludeScope>system</excludeScope>
                                <excludeGroupIds>io.syrix</excludeGroupIds>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${apache-maven-javadoc-plugin.version}</version>
                </plugin>

                <!-- Exec Maven Plugin (for frontend) -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${apache-exec-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- Repository configuration -->
    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- Profile configuration -->
    <profiles>
        <profile>
            <id>no-tests</id>
            <properties>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
    </profiles>
</project>

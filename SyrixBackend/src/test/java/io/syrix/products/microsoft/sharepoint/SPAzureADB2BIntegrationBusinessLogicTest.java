package io.syrix.products.microsoft.sharepoint;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.AllowToBeDeleted;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for SharePoint Azure AD B2B business logic without mocking the core transformation
 * Tests real property extraction and model transformation for Azure AD B2B integration (CIS 7.2.2)
 */
@DisplayName("SharePoint Azure AD B2B Integration Business Logic Tests")
class SPAzureADB2BIntegrationBusinessLogicTest {

    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("Should correctly transform tenant properties with Azure AD B2B integration enabled")
    void shouldTransformTenantPropertiesWithAzureADB2BIntegrationEnabled() {
        // Given - real TenantProperties with Azure AD B2B integration enabled (CIS 7.2.2)
        TenantProperties realTenantProps = new TenantProperties();
        realTenantProps.enableAzureADB2BIntegration = true;  // CIS 7.2.2 target value
        realTenantProps.sharingCapability = 2; // ExternalUserAndGuestSharing
        realTenantProps.defaultSharingLinkType = 1; // Specific People
        realTenantProps.defaultLinkPermission = 1; // View only
        realTenantProps.odbSharingCapability = 2; // ExternalUserAndGuestSharing
        realTenantProps.requireAcceptingAccountMatchInvitedAccount = true;
        realTenantProps.disallowInfectedFileDownload = true;
        
        AllowToBeDeleted realAllowToBeDeleted = new AllowToBeDeleted();
        realAllowToBeDeleted.allowToBeDeletedSPO = false;
        realAllowToBeDeleted.allowToBeDeletedODB = false;

        // When - apply real business logic transformation (no mocking)
        SharePointTenantProperties result = new SharePointTenantProperties(
            realTenantProps, 
            realAllowToBeDeleted.allowToBeDeletedSPO, 
            realAllowToBeDeleted.allowToBeDeletedODB
        );

        // Then - verify Azure AD B2B integration property is correctly set
        assertNotNull(result, "SharePointTenantProperties should not be null");
        assertTrue(result.enableAzureADB2BIntegration, "Azure AD B2B integration should be enabled");
        
        // Verify other related properties for complete B2B configuration
        assertEquals(2, (int) result.sharingCapability, "Sharing capability should allow external users and guests");
        assertEquals(2, (int) result.oneDriveSharingCapability, "OneDrive sharing should allow external users and guests");
        assertTrue(result.requireAcceptingAccountMatchInvitedAccount, "Account matching should be required");
        assertTrue(result.disallowInfectedFileDownload, "Malware protection should be enabled");
    }

    @Test
    @DisplayName("Should correctly transform tenant properties with Azure AD B2B integration disabled")
    void shouldTransformTenantPropertiesWithAzureADB2BIntegrationDisabled() {
        // Given - real TenantProperties with Azure AD B2B integration disabled (default CIS 7.2.2 value)
        TenantProperties realTenantProps = new TenantProperties();
        realTenantProps.enableAzureADB2BIntegration = false;  // CIS 7.2.2 default value
        realTenantProps.sharingCapability = 0; // OnlyUsersInOrg
        realTenantProps.defaultSharingLinkType = 1; // Specific People
        realTenantProps.defaultLinkPermission = 1; // View only
        realTenantProps.odbSharingCapability = 0; // OnlyUsersInOrg
        
        AllowToBeDeleted realAllowToBeDeleted = new AllowToBeDeleted();
        realAllowToBeDeleted.allowToBeDeletedSPO = false;
        realAllowToBeDeleted.allowToBeDeletedODB = false;

        // When - apply real business logic transformation (no mocking)
        SharePointTenantProperties result = new SharePointTenantProperties(
            realTenantProps, 
            realAllowToBeDeleted.allowToBeDeletedSPO, 
            realAllowToBeDeleted.allowToBeDeletedODB
        );

        // Then - verify Azure AD B2B integration property is correctly set to false
        assertNotNull(result, "SharePointTenantProperties should not be null");
        assertFalse(result.enableAzureADB2BIntegration, "Azure AD B2B integration should be disabled");
        
        // Verify restrictive sharing configuration when B2B is disabled
        assertEquals(0, (int) result.sharingCapability, "Sharing capability should be restricted to org users only");
        assertEquals(0, (int) result.oneDriveSharingCapability, "OneDrive sharing should be restricted to org users only");
    }

    @Test
    @DisplayName("Should handle null Azure AD B2B integration property gracefully")
    void shouldHandleNullAzureADB2BIntegrationPropertyGracefully() {
        // Given - real TenantProperties with null Azure AD B2B integration (unset state)
        TenantProperties realTenantProps = new TenantProperties();
        realTenantProps.enableAzureADB2BIntegration = null;  // Unset/unknown state
        realTenantProps.sharingCapability = 1; // ExternalUserSharingOnly
        realTenantProps.defaultSharingLinkType = 1; // Specific People
        realTenantProps.defaultLinkPermission = 1; // View only
        realTenantProps.odbSharingCapability = 1; // ExternalUserSharingOnly
        
        AllowToBeDeleted realAllowToBeDeleted = new AllowToBeDeleted();
        realAllowToBeDeleted.allowToBeDeletedSPO = false;
        realAllowToBeDeleted.allowToBeDeletedODB = false;

        // When - apply real business logic transformation (no mocking)
        SharePointTenantProperties result = new SharePointTenantProperties(
            realTenantProps, 
            realAllowToBeDeleted.allowToBeDeletedSPO, 
            realAllowToBeDeleted.allowToBeDeletedODB
        );

        // Then - verify null handling
        assertNotNull(result, "SharePointTenantProperties should not be null");
        assertNull(result.enableAzureADB2BIntegration, "Azure AD B2B integration should remain null when source is null");
        
        // Verify other properties are still correctly transformed
        assertEquals(1, (int) result.sharingCapability, "Sharing capability should be set correctly");
        assertEquals(1, (int) result.oneDriveSharingCapability, "OneDrive sharing should be set correctly");
    }

    @Test
    @DisplayName("Should properly serialize/deserialize Azure AD B2B integration property")
    void shouldProperlySerializeDeserializeAzureADB2BIntegrationProperty() throws Exception {
        // Given - SharePointTenantProperties with Azure AD B2B integration enabled
        SharePointTenantProperties original = new SharePointTenantProperties();
        original.enableAzureADB2BIntegration = true;
        original.sharingCapability = 2;
        original.oneDriveSharingCapability = 2;
        original.objectIdentity = "test-tenant-id";

        // When - serialize to JSON and deserialize back
        String jsonString = objectMapper.writeValueAsString(original);
        assertNotNull(jsonString, "JSON serialization should not be null");
        assertTrue(jsonString.contains("enableAzureADB2BIntegration"), "JSON should contain enableAzureADB2BIntegration property");

        SharePointTenantProperties deserialized = objectMapper.readValue(jsonString, SharePointTenantProperties.class);

        // Then - verify deserialization preserves Azure AD B2B integration setting
        assertNotNull(deserialized, "Deserialized object should not be null");
        assertEquals(original.enableAzureADB2BIntegration, deserialized.enableAzureADB2BIntegration, 
            "Azure AD B2B integration setting should be preserved");
        assertEquals(original.sharingCapability, deserialized.sharingCapability, 
            "Sharing capability should be preserved");
        assertEquals(original.oneDriveSharingCapability, deserialized.oneDriveSharingCapability, 
            "OneDrive sharing capability should be preserved");
        assertEquals(original.objectIdentity, deserialized.objectIdentity, 
            "Object identity should be preserved");
    }

    @Test
    @DisplayName("Should validate CIS 7.2.2 compliance scenario")
    void shouldValidateCIS722ComplianceScenario() {
        // Given - Default tenant state (non-compliant with CIS 7.2.2)
        TenantProperties nonCompliantTenantProps = new TenantProperties();
        nonCompliantTenantProps.enableAzureADB2BIntegration = false;  // Default: false (non-compliant)
        nonCompliantTenantProps.sharingCapability = 0; // OnlyUsersInOrg
        nonCompliantTenantProps.odbSharingCapability = 0; // OnlyUsersInOrg

        SharePointTenantProperties nonCompliantResult = new SharePointTenantProperties(
            nonCompliantTenantProps, false, false
        );

        // Then - verify non-compliant state
        assertFalse(nonCompliantResult.enableAzureADB2BIntegration, 
            "Default tenant should have Azure AD B2B integration disabled (non-compliant)");

        // Given - Compliant tenant state (after remediation)
        TenantProperties compliantTenantProps = new TenantProperties();
        compliantTenantProps.enableAzureADB2BIntegration = true;  // Target: true (compliant)
        compliantTenantProps.sharingCapability = 2; // ExternalUserAndGuestSharing
        compliantTenantProps.odbSharingCapability = 2; // ExternalUserAndGuestSharing

        SharePointTenantProperties compliantResult = new SharePointTenantProperties(
            compliantTenantProps, false, false
        );

        // Then - verify compliant state
        assertTrue(compliantResult.enableAzureADB2BIntegration, 
            "Remediated tenant should have Azure AD B2B integration enabled (compliant)");
        assertEquals(2, (int) compliantResult.sharingCapability, 
            "Sharing capability should support external users and guests for B2B");
        assertEquals(2, (int) compliantResult.oneDriveSharingCapability, 
            "OneDrive sharing should support external users and guests for B2B");
    }
}